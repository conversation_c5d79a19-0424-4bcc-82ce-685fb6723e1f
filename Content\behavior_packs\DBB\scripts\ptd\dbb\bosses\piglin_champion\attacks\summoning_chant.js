import { spawnEntitiesWithInterval } from "../../../utilities/summonEntity";
import { getRandomLocation } from "../../../utilities/vector3";
/**
 * Generates a random combination of minions for summoning
 * @returns Array of entity configurations for spawning
 */
function generateMinionCombination() {
    const combinations = [
        // 3 Brutes
        [{ entityId: "ptd_dbb:piglin_brute", count: 3 }],
        // 3 Marauders
        [{ entityId: "ptd_dbb:piglin_marauder", count: 3 }],
        // 2 Brutes, 1 Marauder
        [
            { entityId: "ptd_dbb:piglin_brute", count: 2 },
            { entityId: "ptd_dbb:piglin_marauder", count: 1 }
        ],
        // 1 Brute, 2 Marauders
        [
            { entityId: "ptd_dbb:piglin_brute", count: 1 },
            { entityId: "ptd_dbb:piglin_marauder", count: 2 }
        ]
    ];
    // Randomly select one of the combinations
    const randomIndex = Math.floor(Math.random() * combinations.length);
    return combinations[randomIndex];
}
/**
 * Executes the summoning chant attack for the Piglin Champion
 * Summons 3 piglin minions progressively (could be 3 marauders, 3 brutes, or a mix)
 *
 * @param piglinChampion The piglin champion entity
 */
export async function executeSummoningChantAttack(piglinChampion) {
    // Get the piglin champion's unique ID for tagging minions
    const championId = piglinChampion.id;
    // Generate a random combination of minions
    const entityConfigs = generateMinionCombination();
    // Spawn the minions with a delay between each
    await spawnEntitiesWithInterval(piglinChampion.dimension, entityConfigs, () => {
        // Use getRandomLocation to get a random position around the piglin
        // baseOffset: 3, additionalOffset: 2, randomYOffset: 0, checkForAirBlock: true
        const pos = getRandomLocation(piglinChampion.location, piglinChampion.dimension, 3, // Base offset (minimum distance from piglin)
        4, // Additional offset (random extra distance)
        0, // No Y offset
        true // Check for air block
        );
        // If we got a valid position, add visual effects
        if (pos) {
            piglinChampion.dimension.spawnParticle("minecraft:large_explosion", pos);
        }
        return pos;
    }, 15, // 15 ticks delay between spawns (0.75 seconds)
    (entity) => {
        // Tag the minion with the piglin champion's ID immediately after spawning
        entity.setDynamicProperty("ptd_dbb:champion_id", championId);
        // Play a sound and visual effect
        piglinChampion.dimension.playSound("random.totem", entity.location, { volume: 200, pitch: 0.9 });
        piglinChampion.dimension.spawnParticle("ptd_dbb:pg_summon2_01", entity.location);
    });
    return;
}
