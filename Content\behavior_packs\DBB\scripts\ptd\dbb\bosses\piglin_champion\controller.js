import { getDistance } from "../../utilities/vector3";
import { executeHorizontalAttack } from "./attacks/horizontal";
import { executeVerticalAttack } from "./attacks/vertical";
import { executeFootStompAttack } from "./attacks/foot_stomp";
import { executeSpinSlamAttack } from "./attacks/spin_slam";
import { executeBodySlamAttack } from "./attacks/body_slam";
import { executeUpchuckAttack } from "./attacks/upchuck";
import { executeChargingAttack, startContinuousChargingDamage } from "./attacks/charging";
import { executeSummoningChantAttack } from "./attacks/summoning_chant";
import { executeHealingAbility } from "./abilities/healing";
import { getAvailableAttacks, updateAttackHistory, displayAttackHistory, SHORT_RANGE_ATTACKS, MEDIUM_RANGE_ATTACKS, LONG_RANGE_ATTACKS } from "./attackTracker";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";
import { getTarget } from "../general_mechanics/targetUtils";
/**
 * Counts the number of minions belonging to a specific piglin champion within a given radius
 * @param piglinChampion The piglin champion entity
 * @param radius The search radius in blocks (default: 64)
 * @returns The number of minions belonging to this piglin champion
 */
export function countPiglinChampionMinions(piglinChampion, radius = 64) {
    // Get the piglin champion's unique ID
    const championId = piglinChampion.id;
    // Search for piglin minions in the area
    const minions = piglinChampion.dimension.getEntities({
        location: piglinChampion.location,
        maxDistance: radius,
        type: ["ptd_dbb:piglin_brute", "ptd_dbb:piglin_marauder"]
    });
    // Count minions that belong to this specific piglin champion
    let count = 0;
    for (const minion of minions) {
        const minionChampionId = minion.getDynamicProperty("ptd_dbb:champion_id");
        if (minionChampionId === championId) {
            count++;
        }
    }
    return count;
}
/**
 * Attack durations in ticks (20 ticks per second)
 * The total duration of the animation
 */
export const ATTACK_DURATIONS = {
    horizontal: 104, // 5.2 seconds at 20 ticks per second
    vertical: 106, // 5.3 seconds at 20 ticks per second
    foot_stomp: 80, // 4.0 seconds at 20 ticks per second
    spin_slam: 178, // 8.9 seconds at 20 ticks per second
    body_slam: 186, // 9.3 seconds at 20 ticks per second
    upchuck: 170, // 8.5 seconds at 20 ticks per second
    charging: 315, // 15.75 seconds at 20 ticks per second
    healing: 158, // 7.9 seconds at 20 ticks per second
    summoning_chant: 125, // 6.25 seconds at 20 ticks per second
    stunned_standing: 240, // 12 seconds at 20 ticks per second
    stunned_sitting: 333 // 16.65 seconds at 20 ticks per second
};
/**
 * Attack timing points in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMINGS = {
    horizontal: 38,
    vertical: 44,
    foot_stomp: 24,
    spin_slam_phase1: 83,
    spin_slam_phase2: 124,
    body_slam: 63,
    upchuck: 55,
    charging_continuous: 46, // Start continuous damage during charge movement
    charging: 96, // Final impact damage
    healing_phase1: 43,
    healing_phase2: 130,
    summoning_chant: 40,
    stunned_standing_phase1: 30, // End of damage_to_stunned
    stunned_standing_phase2: 150, // End of stunned_standing (30 + 120)
    stunned_sitting_phase1: 30, // End of damage_to_stunned
    stunned_sitting_phase2: 150 // End of stunned_sitting (30 + 120)
};
/**
 * Attack range boundaries in blocks
 * Defines strict minimum and maximum distances for each attack range category
 */
export const ATTACK_RANGES = {
    close: { min: 0, max: 5 }, // 0-5 blocks: all attack types available
    medium: { min: 5, max: 7 }, // 5-7 blocks: all attack types with different probabilities
    long: { min: 7, max: 8 }, // 7-8 blocks: vertical attack, upchuck, and charging
    unreachable: { min: 12, max: 32 } // 12-32 blocks: charging attack and summoning chant
};
/**
 * Selects an attack based on target distance
 * @param piglinChampion The piglin champion entity
 * @param target The target entity
 */
export function selectAttack(piglinChampion, target) {
    const distance = getDistance(piglinChampion.location, target.location);
    let selectedAttack = null;
    // Close range (0-5 blocks): select from available attacks based on usage history
    if (distance >= ATTACK_RANGES.close.min && distance <= ATTACK_RANGES.close.max) {
        // Get available attacks based on usage history
        const availableAttacks = getAvailableAttacks(piglinChampion, SHORT_RANGE_ATTACKS);
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopPiglinChampionSounds(piglinChampion, attackSound);
            // Trigger the attack event
            piglinChampion.triggerEvent(`ptd_dbb:${attack}_attack`);
            // Update attack history
            updateAttackHistory(piglinChampion, attack);
            // Display attack history on the actionbar
            displayAttackHistory(piglinChampion);
        }
    }
    // Medium range (4-8 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.medium.min && distance <= ATTACK_RANGES.medium.max) {
        // Get available attacks based on usage history
        let availableAttacks = getAvailableAttacks(piglinChampion, MEDIUM_RANGE_ATTACKS);
        // Check minion count for summoning_chant attack
        const minionCount = countPiglinChampionMinions(piglinChampion, 64);
        if (minionCount > 1) {
            // Remove summoning_chant from available attacks if there are more than 1 minion
            availableAttacks = availableAttacks.filter(attack => attack !== "summoning_chant");
        }
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopPiglinChampionSounds(piglinChampion, attackSound);
            // Trigger the attack event
            piglinChampion.triggerEvent(`ptd_dbb:${attack}_attack`);
            // Update attack history
            updateAttackHistory(piglinChampion, attack);
            // Display attack history on the actionbar
            displayAttackHistory(piglinChampion);
        }
    }
    // Long range (8-12 blocks): select from available attacks based on usage history
    else if (distance > ATTACK_RANGES.long.min && distance <= ATTACK_RANGES.long.max) {
        // Get available attacks based on usage history
        let availableAttacks = getAvailableAttacks(piglinChampion, LONG_RANGE_ATTACKS);
        // Check minion count for summoning_chant attack
        const minionCount = countPiglinChampionMinions(piglinChampion, 64);
        if (minionCount > 1) {
            // Remove summoning_chant from available attacks if there are more than 1 minion
            availableAttacks = availableAttacks.filter(attack => attack !== "summoning_chant");
        }
        if (availableAttacks.length > 0) {
            // Randomly select one of the available attacks
            const randomIndex = Math.floor(Math.random() * availableAttacks.length);
            const attack = availableAttacks[randomIndex];
            selectedAttack = attack;
            // Stop all other sound effects except for this attack's sound
            const attackSound = ATTACK_SOUND_MAP[attack];
            stopPiglinChampionSounds(piglinChampion, attackSound);
            // Trigger the attack event
            piglinChampion.triggerEvent(`ptd_dbb:${attack}_attack`);
            // Update attack history
            updateAttackHistory(piglinChampion, attack);
            // Display attack history on the actionbar
            displayAttackHistory(piglinChampion);
        }
    }
    // Unreachable range (12+ blocks): charging attack or summoning_chant
    else if (distance > ATTACK_RANGES.unreachable.min && distance <= ATTACK_RANGES.unreachable.max) {
        // 20% chance for an attack, 80% chance to not attack
        // this is evaluated per tick
        if (Math.random() < 0.2) {
            // Check minion count for summoning_chant attack
            const minionCount = countPiglinChampionMinions(piglinChampion, 64);
            // If there are 0-1 minions, allow summoning_chant (20% chance), otherwise only charging
            if (minionCount <= 1 && Math.random() < 0.2) {
                selectedAttack = "summoning_chant";
                // Stop all other sound effects except for this attack's sound
                const attackSound = ATTACK_SOUND_MAP["summoning_chant"];
                stopPiglinChampionSounds(piglinChampion, attackSound);
                piglinChampion.triggerEvent("ptd_dbb:summoning_chant_attack");
            }
            else {
                selectedAttack = "charging";
                // Stop all other sound effects except for this attack's sound
                const attackSound = ATTACK_SOUND_MAP["charging"];
                stopPiglinChampionSounds(piglinChampion, attackSound);
                piglinChampion.triggerEvent("ptd_dbb:charging_attack");
            }
        }
    }
    // Apply slowness effect if an attack was selected
    if (selectedAttack && selectedAttack in ATTACK_DURATIONS) {
        const attackDuration = ATTACK_DURATIONS[selectedAttack];
        if (attackDuration) {
            if (selectedAttack === "charging") {
                // Apply slowness with amplifier 250 for only 30 ticks
                // for the charging attack since it requires movement
                piglinChampion.addEffect("minecraft:slowness", 30, { amplifier: 250, showParticles: false });
                return;
            }
            else {
                // Apply slowness with amplifier 250 for the duration of the attack
                piglinChampion.addEffect("minecraft:slowness", attackDuration, { amplifier: 250, showParticles: false });
            }
        }
    }
}
/**
 * Handles attack logic based on attack type and timer
 * @param piglinChampion The piglin champion entity
 * @param target The target entity
 * @param attack The current attack type
 * @param attackTimer The current attack timer
 */
export function handleAttackLogic(piglinChampion, attack, attackTimer, target) {
    // Execute horizontal attack at the right timing
    if (attack === "horizontal" && attackTimer === ATTACK_TIMINGS.horizontal) {
        executeHorizontalAttack(piglinChampion);
    }
    // Execute vertical attack at the right timing
    else if (attack === "vertical" && attackTimer === ATTACK_TIMINGS.vertical) {
        // Use provided target or get one using the getTarget utility function
        if (!target) {
            target = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
        }
        // Only execute if we have a valid target
        if (target) {
            executeVerticalAttack(piglinChampion, target);
        }
    }
    // Execute foot stomp attack at the right timing
    else if (attack === "foot_stomp" && attackTimer === ATTACK_TIMINGS.foot_stomp) {
        executeFootStompAttack(piglinChampion);
    }
    // Execute spin slam attack at the right timings (two phases)
    else if (attack === "spin_slam" && attackTimer === ATTACK_TIMINGS.spin_slam_phase1) {
        executeSpinSlamAttack(piglinChampion, 1); // Phase 1: Lift
    }
    else if (attack === "spin_slam" && attackTimer === ATTACK_TIMINGS.spin_slam_phase2) {
        executeSpinSlamAttack(piglinChampion, 2); // Phase 2: Slam
    }
    // Execute body slam attack at the right timing
    else if (attack === "body_slam" && attackTimer === ATTACK_TIMINGS.body_slam) {
        executeBodySlamAttack(piglinChampion);
    }
    // Execute upchuck attack at the right timing
    else if (attack === "upchuck" && attackTimer === ATTACK_TIMINGS.upchuck) {
        executeUpchuckAttack(piglinChampion);
    }
    // Start continuous charging damage during charge movement
    else if (attack === "charging" && attackTimer === ATTACK_TIMINGS.charging_continuous) {
        startContinuousChargingDamage(piglinChampion);
    }
    // Execute final charging attack impact at the right timing
    else if (attack === "charging" && attackTimer === ATTACK_TIMINGS.charging) {
        executeChargingAttack(piglinChampion);
    }
    // Apply speed 3 effect at the start of charge2 animation (tick 30)
    else if (attack === "charging" && attackTimer === 30) {
        // Apply speed 3 effect for the duration of charge_2 and charge_3 (60 ticks)
        piglinChampion.addEffect("minecraft:speed", 60, { amplifier: 3, showParticles: false });
        piglinChampion.triggerEvent("ptd_dbb:charging2"); // Increase speed in the component groups
    }
    // Apply slowness effect at the start of stunned_sitting animation (tick 90)
    else if (attack === "charging" && attackTimer === 90) {
        // Apply slowness for the duration of stunned_sitting and charge_4 (220 ticks)
        piglinChampion.addEffect("minecraft:slowness", 220, { amplifier: 250, showParticles: false });
        piglinChampion.triggerEvent("ptd_dbb:stun_after_charge"); // Stun the piglin - remove the melee component group temporarily
    }
    // Disable look_at_target animation during stunned animations
    else if ((attack === "stunned_standing" || attack === "stunned_sitting") && attackTimer === 1) {
        // Disable look_at_target animation
        piglinChampion.triggerEvent("ptd_dbb:stun_after_charge"); // Reuse the existing event that disables melee
    }
    // Execute healing ability at the right timings (two phases)
    else if (attack === "healing" && attackTimer === ATTACK_TIMINGS.healing_phase1) {
        executeHealingAbility(piglinChampion, 1); // Phase 1: Healing
    }
    else if (attack === "healing" && attackTimer === ATTACK_TIMINGS.healing_phase2) {
        executeHealingAbility(piglinChampion, 2); // Phase 2: Knockback
    }
    // Execute summoning chant attack at the right timing
    else if (attack === "summoning_chant" && attackTimer === ATTACK_TIMINGS.summoning_chant) {
        executeSummoningChantAttack(piglinChampion);
    }
    // Reset attack when animation is complete
    const duration = ATTACK_DURATIONS[attack];
    if (duration !== undefined && attackTimer === duration) {
        // Only trigger reset when the timer exactly matches the duration to prevent multiple triggers
        // If healing is complete, trigger upchuck attack instead of resetting
        if (attack === "healing") {
            // Stop all other sound effects except for upchuck sound
            const upchuckSound = ATTACK_SOUND_MAP["upchuck"];
            stopPiglinChampionSounds(piglinChampion, upchuckSound);
            piglinChampion.triggerEvent("ptd_dbb:upchuck_attack");
            // Apply slowness effect for the duration of the upchuck animation
            const upchuckDuration = ATTACK_DURATIONS.upchuck || 170; // Fallback to 170 ticks if undefined
            piglinChampion.addEffect("minecraft:slowness", upchuckDuration, { amplifier: 250, showParticles: false });
        }
        else {
            // Stop all sounds when resetting attack
            stopPiglinChampionSounds(piglinChampion);
            piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            // Recover from stun after charging attack - add the melee component group back
            if (attack === "charging") {
                piglinChampion.triggerEvent("ptd_dbb:recover_after_charge");
            }
        }
    }
    return;
}
